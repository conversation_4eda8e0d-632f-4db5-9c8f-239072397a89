import React, { useState, useEffect, useMemo, useCallback, memo } from 'react';
import {
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Box,
  useTheme,
  CircularProgress,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Avatar,
  CardActionArea
} from "@mui/material";
import { Link as RouterLink } from "react-router-dom";
import { motion } from "framer-motion";
import {
  People as PeopleIcon,
  Business as BusinessIcon,
  AttachMoney as AttachMoneyIcon,
  Timeline as TimelineIcon,
  Calculate as CalculateIcon,
  CompareArrows as CompareArrowsIcon,
  AccountBalance as AccountBalanceIcon
} from "@mui/icons-material";
import BarChart from './charts/BarChart';
import PieChart from './charts/PieChart';
import LineChart from './charts/LineChart';
import { loadCostCenterData, loadSalaryData } from "../db";

const Dashboard = () => {
  const theme = useTheme();
  const [loading, setLoading] = useState(true);
  const [selectedMonth, setSelectedMonth] = useState('');
  const [availableMonths, setAvailableMonths] = useState([]);
  const [costCenterData, setCostCenterData] = useState([]);
  const [salaryData, setSalaryData] = useState([]);
  // Removed unused state variables for universal filter and filtered data
  const [costCenterStats, setCostCenterStats] = useState({
    totalHours: 0,
    totalEmployees: 0,
    avgHoursPerEmployee: 0,
    costCenterDistribution: []
  });
  const [salaryStats, setSalaryStats] = useState({
    totalGross: 0,
    totalPF: 0,
    avgSalary: 0,
    employeeCount: 0
  });
  const [monthlyTrends, setMonthlyTrends] = useState({
    labels: [],
    grossSalary: [],
    pfContributions: [],
    hoursWorked: []
  });

  // Load data when component mounts
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Load cost center and salary data
        const ccData = await loadCostCenterData();
        const slData = await loadSalaryData();

        setCostCenterData(ccData);
        setSalaryData(slData);

        // Extract unique months
        const ccMonths = ccData.map(item => item.month?.trim()).filter(Boolean);
        const slMonths = slData.map(item => item.payrollMonth?.trim()).filter(Boolean);
        const allMonths = [...new Set([...ccMonths, ...slMonths])];
        setAvailableMonths(allMonths.sort());

        // Set default selected month to the most recent one
        if (allMonths.length > 0) {
          setSelectedMonth(allMonths[allMonths.length - 1]);
        }

        // Calculate overall stats
        calculateStats(ccData, slData, allMonths.length > 0 ? allMonths[allMonths.length - 1] : '');
      } catch (error) {
        console.error('Error loading dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();

    // Listen for database changes
    const handleDBChange = () => fetchData();
    window.addEventListener('dbChange', handleDBChange);
    return () => window.removeEventListener('dbChange', handleDBChange);
  }, []);

  // Recalculate stats when selected month changes
  useEffect(() => {
    if (selectedMonth) {
      calculateStats(costCenterData, salaryData, selectedMonth);
    }
  }, [selectedMonth, costCenterData, salaryData]);

  // Function to calculate statistics based on the data
  const calculateStats = (ccData, slData, month) => {
    // Filter data for the selected month
    const filteredCC = month ? ccData.filter(item => item.month?.trim() === month) : ccData;
    const filteredSalary = month ? slData.filter(item => item.payrollMonth?.trim() === month) : slData;

    // Calculate cost center statistics
    const uniqueEmployees = new Set(filteredCC.map(item => item.name?.trim()).filter(Boolean));
    const totalHours = filteredCC.reduce((sum, item) => sum + (parseFloat(item.hoursWorked) || 0), 0);

    // Calculate cost center distribution
    const ccGroups = {};
    filteredCC.forEach(item => {
      const cc = item.costCenter?.trim();
      if (cc) {
        if (!ccGroups[cc]) ccGroups[cc] = 0;
        ccGroups[cc] += parseFloat(item.hoursWorked) || 0;
      }
    });

    const distribution = Object.entries(ccGroups).map(([name, hours]) => ({
      name,
      hours,
      percentage: totalHours > 0 ? (hours / totalHours) * 100 : 0
    }));

    setCostCenterStats({
      totalHours,
      totalEmployees: uniqueEmployees.size,
      avgHoursPerEmployee: uniqueEmployees.size > 0 ? totalHours / uniqueEmployees.size : 0,
      costCenterDistribution: distribution
    });

    // Calculate salary statistics
    const totalGross = filteredSalary.reduce((sum, item) => sum + (parseFloat(item.gross) || 0), 0);
    const totalPF = filteredSalary.reduce((sum, item) => sum + (parseFloat(item.pf) || 0), 0);

    setSalaryStats({
      totalGross,
      totalPF,
      avgSalary: filteredSalary.length > 0 ? totalGross / filteredSalary.length : 0,
      employeeCount: filteredSalary.length
    });

    // Calculate monthly trends (for all months)
    const monthsSet = new Set([...ccData.map(item => item.month?.trim()), ...slData.map(item => item.payrollMonth?.trim())]);
    const months = Array.from(monthsSet).filter(Boolean).sort();

    const grossByMonth = {};
    const pfByMonth = {};
    const hoursByMonth = {};

    months.forEach(m => {
      grossByMonth[m] = 0;
      pfByMonth[m] = 0;
      hoursByMonth[m] = 0;
    });

    slData.forEach(item => {
      const month = item.payrollMonth?.trim();
      if (month && grossByMonth[month] !== undefined) {
        grossByMonth[month] += parseFloat(item.gross) || 0;
        pfByMonth[month] += parseFloat(item.pf) || 0;
      }
    });

    ccData.forEach(item => {
      const month = item.month?.trim();
      if (month && hoursByMonth[month] !== undefined) {
        hoursByMonth[month] += parseFloat(item.hoursWorked) || 0;
      }
    });

    setMonthlyTrends({
      labels: months,
      grossSalary: months.map(m => grossByMonth[m] || 0),
      pfContributions: months.map(m => pfByMonth[m] || 0),
      hoursWorked: months.map(m => hoursByMonth[m] || 0)
    });
  };

  // Chart data for cost center distribution
  const costCenterChartData = {
    labels: costCenterStats.costCenterDistribution.map(item => item.name),
    datasets: [
      {
        label: 'Hours Distribution',
        data: costCenterStats.costCenterDistribution.map(item => item.hours),
        backgroundColor: [
          theme.palette.primary.main,
          theme.palette.secondary.main,
          theme.palette.success.main,
          theme.palette.info.main,
          theme.palette.warning.main,
          theme.palette.error.main,
          ...Array(20).fill(0).map(() => `rgba(${Math.floor(Math.random() * 255)}, ${Math.floor(Math.random() * 255)}, ${Math.floor(Math.random() * 255)}, 0.7)`)
        ],
        borderWidth: 1,
      },
    ],
  };

  // Chart data for monthly trends
  const monthlyTrendsChartData = {
    labels: monthlyTrends.labels,
    datasets: [
      {
        label: 'Gross Salary',
        data: monthlyTrends.grossSalary,
        borderColor: theme.palette.primary.main,
        backgroundColor: 'rgba(25, 118, 210, 0.1)',
        fill: true,
        tension: 0.4,
      },
      {
        label: 'PF Contributions',
        data: monthlyTrends.pfContributions,
        borderColor: theme.palette.secondary.main,
        backgroundColor: 'rgba(156, 39, 176, 0.1)',
        fill: true,
        tension: 0.4,
      },
    ],
  };

  // Chart data for hours worked
  const hoursWorkedChartData = {
    labels: monthlyTrends.labels,
    datasets: [
      {
        label: 'Hours Worked',
        data: monthlyTrends.hoursWorked,
        backgroundColor: theme.palette.info.main,
      },
    ],
  };

  // Define dashboard cards
  const dashboardCards = [
    {
      title: 'Cost Center',
      description: 'Manage and view cost center data',
      icon: <BusinessIcon fontSize="large" sx={{ color: theme.palette.primary.main }} />,
      path: '/cost-center',
      color: theme.palette.primary.light
    },
    {
      title: 'Salary',
      description: 'Manage employee salary information',
      icon: <AttachMoneyIcon fontSize="large" sx={{ color: theme.palette.success.main }} />,
      path: '/salary',
      color: theme.palette.success.light
    },
    {
      title: 'Resource Utilization',
      description: 'Track resource allocation and usage',
      icon: <TimelineIcon fontSize="large" sx={{ color: theme.palette.info.main }} />,
      path: '/resource-utilization',
      color: theme.palette.info.light
    },
    {
      title: 'Resource Cost',
      description: 'Analyze costs associated with resources',
      icon: <PeopleIcon fontSize="large" sx={{ color: theme.palette.warning.main }} />,
      path: '/resource-cost',
      color: theme.palette.warning.light
    },
    {
      title: 'Employee Discrepancy',
      description: 'Identify and resolve employee data discrepancies',
      icon: <CompareArrowsIcon fontSize="large" sx={{ color: theme.palette.error.main }} />,
      path: '/employee-discrepancy',
      color: theme.palette.error.light
    },
    {
      title: 'Tally Output',
      description: 'View financial tally and reconciliation',
      icon: <CalculateIcon fontSize="large" sx={{ color: theme.palette.secondary.main }} />,
      path: '/tally',
      color: theme.palette.secondary.light
    }
  ];

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Container maxWidth="xl" sx={{ py: 4 }}>
        {/* Header with Month Selector */}
        <Box mb={4} display="flex" justifyContent="space-between" alignItems="center">
          <Box>
            <Typography variant="h4" component="h1" gutterBottom>
              Financial Dashboard
            </Typography>
            <Typography variant="subtitle1" color="text.secondary" gutterBottom>
              Welcome to the Financial Reporting Dashboard
            </Typography>
          </Box>

          <FormControl sx={{ minWidth: 200 }}>
            <InputLabel id="dashboard-month-label">Select Month</InputLabel>
            <Select
              labelId="dashboard-month-label"
              value={selectedMonth}
              label="Select Month"
              onChange={(e) => setSelectedMonth(e.target.value)}
            >
              {availableMonths.map((month) => (
                <MenuItem key={month} value={month}>
                  {month}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>

        {loading ? (
          <Box display="flex" justifyContent="center" alignItems="center" height="50vh">
            <CircularProgress />
            <Typography variant="h6" sx={{ ml: 2 }}>Loading dashboard data...</Typography>
          </Box>
        ) : (
          <>
            {/* Key Statistics Cards */}
            <Grid container spacing={3} mb={4}>
              <Grid item xs={12} sm={6} md={3}>
                <Card sx={{ height: '100%', boxShadow: '0 4px 12px rgba(0,0,0,0.05)', borderRadius: 2 }}>
                  <CardContent>
                    <Box display="flex" alignItems="center" mb={1}>
                      <Avatar sx={{ bgcolor: theme.palette.primary.main, mr: 2 }}>
                        <PeopleIcon />
                      </Avatar>
                      <Typography variant="h6">Employees</Typography>
                    </Box>
                    <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
                      {costCenterStats.totalEmployees}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Active employees in {selectedMonth || 'all periods'}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Card sx={{ height: '100%', boxShadow: '0 4px 12px rgba(0,0,0,0.05)', borderRadius: 2 }}>
                  <CardContent>
                    <Box display="flex" alignItems="center" mb={1}>
                      <Avatar sx={{ bgcolor: theme.palette.success.main, mr: 2 }}>
                        <AttachMoneyIcon />
                      </Avatar>
                      <Typography variant="h6">Total Salary</Typography>
                    </Box>
                    <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
                      ₹{salaryStats.totalGross.toLocaleString('en-IN', { maximumFractionDigits: 0 })}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Gross salary for {selectedMonth || 'all periods'}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Card sx={{ height: '100%', boxShadow: '0 4px 12px rgba(0,0,0,0.05)', borderRadius: 2 }}>
                  <CardContent>
                    <Box display="flex" alignItems="center" mb={1}>
                      <Avatar sx={{ bgcolor: theme.palette.info.main, mr: 2 }}>
                        <TimelineIcon />
                      </Avatar>
                      <Typography variant="h6">Total Hours</Typography>
                    </Box>
                    <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
                      {costCenterStats.totalHours.toLocaleString('en-IN', { maximumFractionDigits: 1 })}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Hours worked in {selectedMonth || 'all periods'}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Card sx={{ height: '100%', boxShadow: '0 4px 12px rgba(0,0,0,0.05)', borderRadius: 2 }}>
                  <CardContent>
                    <Box display="flex" alignItems="center" mb={1}>
                      <Avatar sx={{ bgcolor: theme.palette.secondary.main, mr: 2 }}>
                        <AccountBalanceIcon />
                      </Avatar>
                      <Typography variant="h6">PF Contribution</Typography>
                    </Box>
                    <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
                      ₹{salaryStats.totalPF.toLocaleString('en-IN', { maximumFractionDigits: 0 })}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total PF for {selectedMonth || 'all periods'}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>

            {/* Charts Section */}
            <Grid container spacing={3} mb={4}>
              <Grid item xs={12} md={8}>
                <LineChart
                  title="Monthly Salary Trends"
                  data={monthlyTrendsChartData}
                  height={300}
                />
              </Grid>

              <Grid item xs={12} md={4}>
                <PieChart
                  title="Cost Center Distribution"
                  data={costCenterChartData}
                  height={300}
                  options={{
                    plugins: {
                      legend: {
                        position: 'bottom',
                        labels: {
                          boxWidth: 12
                        }
                      }
                    }
                  }}
                />
              </Grid>

              <Grid item xs={12}>
                <BarChart
                  title="Monthly Hours Worked"
                  data={hoursWorkedChartData}
                  height={250}
                />
              </Grid>
            </Grid>

            {/* Cost Center Distribution Table */}
            <Card sx={{ mb: 4, boxShadow: '0 4px 12px rgba(0,0,0,0.05)', borderRadius: 2 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>Cost Center Distribution</Typography>
                <TableContainer component={Paper} sx={{ boxShadow: 'none' }}>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell sx={{ fontWeight: 'bold', color: 'black' }}>Cost Center</TableCell>
                        <TableCell sx={{ fontWeight: 'bold', color: 'black' }}>Hours</TableCell>
                        <TableCell sx={{ fontWeight: 'bold', color: 'black' }}>Percentage</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {costCenterStats.costCenterDistribution.map((item, index) => (
                        <TableRow key={index} sx={{ '&:nth-of-type(even)': { backgroundColor: '#f4f6f8' } }}>
                          <TableCell>{item.name}</TableCell>
                          <TableCell>{item.hours.toFixed(1)}</TableCell>
                          <TableCell>{item.percentage.toFixed(1)}%</TableCell>
                        </TableRow>
                      ))}
                      {costCenterStats.costCenterDistribution.length === 0 && (
                        <TableRow>
                          <TableCell colSpan={3} align="center">No data available</TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </TableContainer>
              </CardContent>
            </Card>

            {/* Navigation Cards */}
            <Typography variant="h5" gutterBottom>Quick Navigation</Typography>
            <Grid container spacing={3}>
              {dashboardCards.map((card, index) => (
                <Grid item xs={12} sm={6} md={4} key={index}>
                  <Card
                    component={motion.div}
                    whileHover={{
                      scale: 1.03,
                      boxShadow: '0 8px 16px rgba(0,0,0,0.1)'
                    }}
                    transition={{ type: 'spring', stiffness: 300 }}
                    sx={{
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      borderRadius: 2,
                      overflow: 'hidden',
                      boxShadow: '0 4px 12px rgba(0,0,0,0.05)'
                    }}
                  >
                    <CardActionArea
                      component={RouterLink}
                      to={card.path}
                      sx={{
                        height: '100%',
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'flex-start',
                        justifyContent: 'flex-start'
                      }}
                    >
                      <Box
                        sx={{
                          width: '100%',
                          height: 8,
                          backgroundColor: card.color
                        }}
                      />
                      <CardContent sx={{ p: 3, width: '100%' }}>
                        <Box
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            mb: 2
                          }}
                        >
                          <Avatar
                            sx={{
                              backgroundColor: 'white',
                              width: 56,
                              height: 56,
                              boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                            }}
                          >
                            {card.icon}
                          </Avatar>
                          <Box sx={{ ml: 2 }}>
                            <Typography variant="h6" component="h2">
                              {card.title}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              {card.description}
                            </Typography>
                          </Box>
                        </Box>
                      </CardContent>
                    </CardActionArea>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </>
        )}
      </Container>
    </motion.div>
  );
};

export default Dashboard;
