// Performance testing utilities for the financial reporting application
import performanceMonitor from './performanceMonitor';
import { getCacheStats } from './dataCache';

/**
 * Performance test suite for the application
 */
class PerformanceTestSuite {
  constructor() {
    this.testResults = [];
  }

  /**
   * Run all performance tests
   */
  async runAllTests() {
    console.group('🚀 Running Performance Tests');
    
    try {
      await this.testDataLoading();
      await this.testDataProcessing();
      await this.testCachePerformance();
      await this.testComponentRendering();
      
      this.generateReport();
    } catch (error) {
      console.error('Performance test failed:', error);
    }
    
    console.groupEnd();
  }

  /**
   * Test data loading performance
   */
  async testDataLoading() {
    console.log('📊 Testing data loading performance...');
    
    const { loadCostCenterData, loadSalaryData } = await import('../db');
    
    // Test cost center data loading
    const endCostCenter = performanceMonitor.startTiming('cost-center-load-test');
    await loadCostCenterData();
    endCostCenter();
    
    // Test salary data loading
    const endSalary = performanceMonitor.startTiming('salary-load-test');
    await loadSalaryData();
    endSalary();
    
    // Test cached loading (should be much faster)
    const endCachedCostCenter = performanceMonitor.startTiming('cost-center-cached-test');
    await loadCostCenterData();
    endCachedCostCenter();
    
    const endCachedSalary = performanceMonitor.startTiming('salary-cached-test');
    await loadSalaryData();
    endCachedSalary();
  }

  /**
   * Test data processing performance
   */
  async testDataProcessing() {
    console.log('⚙️ Testing data processing performance...');
    
    const { loadCostCenterData } = await import('../db');
    const { getUniqueCostCenters } = await import('./costCenterMappings');
    
    const data = await loadCostCenterData();
    
    // Test unique cost centers extraction
    const endUnique = performanceMonitor.startTiming('unique-cost-centers-test');
    getUniqueCostCenters(data);
    endUnique();
    
    // Test cached extraction (should be faster)
    const endCachedUnique = performanceMonitor.startTiming('unique-cost-centers-cached-test');
    getUniqueCostCenters(data);
    endCachedUnique();
    
    // Test large dataset filtering
    const endFiltering = performanceMonitor.startTiming('data-filtering-test');
    const filtered = data.filter(item => 
      item.month && item.month.includes('2024') && 
      item.costCenter && item.costCenter.length > 0
    );
    endFiltering();
    
    console.log(`Filtered ${filtered.length} items from ${data.length} total`);
  }

  /**
   * Test cache performance
   */
  async testCachePerformance() {
    console.log('💾 Testing cache performance...');
    
    const cacheStats = getCacheStats();
    console.log('Cache statistics:', cacheStats);
    
    this.testResults.push({
      test: 'Cache Performance',
      stats: cacheStats,
      passed: cacheStats.active > 0
    });
  }

  /**
   * Test component rendering performance
   */
  async testComponentRendering() {
    console.log('🎨 Testing component rendering performance...');
    
    // Simulate component render timing
    const componentTests = [
      'Tally-render',
      'ResourceUtilization-render',
      'Dashboard-render'
    ];
    
    for (const component of componentTests) {
      const endRender = performanceMonitor.startTiming(`${component}-test`);
      
      // Simulate render work
      await new Promise(resolve => setTimeout(resolve, Math.random() * 50));
      
      endRender();
    }
  }

  /**
   * Generate performance report
   */
  generateReport() {
    console.log('📋 Generating Performance Report...');
    
    const allMetrics = performanceMonitor.getAllMetrics();
    const report = {
      timestamp: new Date().toISOString(),
      metrics: allMetrics,
      cacheStats: getCacheStats(),
      recommendations: this.generateRecommendations(allMetrics)
    };
    
    console.table(this.formatMetricsForTable(allMetrics));
    console.log('Recommendations:', report.recommendations);
    
    return report;
  }

  /**
   * Format metrics for console.table display
   */
  formatMetricsForTable(metrics) {
    const formatted = {};
    
    for (const [name, stats] of Object.entries(metrics)) {
      if (stats) {
        formatted[name] = {
          'Avg Duration (ms)': stats.duration.avg.toFixed(2),
          'Min Duration (ms)': stats.duration.min.toFixed(2),
          'Max Duration (ms)': stats.duration.max.toFixed(2),
          'Calls': stats.count,
          'Avg Memory (MB)': stats.memory.avg.toFixed(2)
        };
      }
    }
    
    return formatted;
  }

  /**
   * Generate performance recommendations
   */
  generateRecommendations(metrics) {
    const recommendations = [];
    
    for (const [name, stats] of Object.entries(metrics)) {
      if (!stats) continue;
      
      // Check for slow operations
      if (stats.duration.avg > 100) {
        recommendations.push(`⚠️ ${name} is slow (avg: ${stats.duration.avg.toFixed(2)}ms). Consider optimization.`);
      }
      
      // Check for memory issues
      if (stats.memory.avg > 10) {
        recommendations.push(`🧠 ${name} uses significant memory (avg: ${stats.memory.avg.toFixed(2)}MB). Check for leaks.`);
      }
      
      // Check for frequent calls
      if (stats.count > 50) {
        recommendations.push(`🔄 ${name} called frequently (${stats.count} times). Consider caching or debouncing.`);
      }
    }
    
    if (recommendations.length === 0) {
      recommendations.push('✅ All operations are performing well!');
    }
    
    return recommendations;
  }

  /**
   * Benchmark a specific operation
   */
  async benchmark(name, operation, iterations = 10) {
    console.log(`🏃 Benchmarking ${name} (${iterations} iterations)...`);
    
    const results = [];
    
    for (let i = 0; i < iterations; i++) {
      const endTiming = performanceMonitor.startTiming(`${name}-benchmark-${i}`);
      await operation();
      endTiming();
      
      const stats = performanceMonitor.getMetricStats(`${name}-benchmark-${i}`);
      if (stats) {
        results.push(stats.duration.latest);
      }
    }
    
    const avg = results.reduce((a, b) => a + b, 0) / results.length;
    const min = Math.min(...results);
    const max = Math.max(...results);
    
    console.log(`${name} benchmark results:`, {
      average: `${avg.toFixed(2)}ms`,
      min: `${min.toFixed(2)}ms`,
      max: `${max.toFixed(2)}ms`,
      iterations
    });
    
    return { avg, min, max, results };
  }
}

// Create singleton instance
const performanceTestSuite = new PerformanceTestSuite();

// Export for use in development
export default performanceTestSuite;

// Helper functions
export const runPerformanceTests = () => performanceTestSuite.runAllTests();
export const benchmarkOperation = (name, operation, iterations) => 
  performanceTestSuite.benchmark(name, operation, iterations);

// Auto-run tests in development mode
if (process.env.NODE_ENV === 'development') {
  // Run tests after a delay to allow app to initialize
  setTimeout(() => {
    if (window.location.search.includes('perf-test')) {
      runPerformanceTests();
    }
  }, 5000);
}
