// Data caching utility to improve performance by reducing API calls
class DataCache {
  constructor() {
    this.cache = new Map();
    this.timestamps = new Map();
    this.defaultTTL = 5 * 60 * 1000; // 5 minutes default TTL
  }

  /**
   * Set data in cache with optional TTL
   * @param {string} key - Cache key
   * @param {any} data - Data to cache
   * @param {number} ttl - Time to live in milliseconds
   */
  set(key, data, ttl = this.defaultTTL) {
    this.cache.set(key, data);
    this.timestamps.set(key, Date.now() + ttl);
  }

  /**
   * Get data from cache
   * @param {string} key - Cache key
   * @returns {any|null} - Cached data or null if not found/expired
   */
  get(key) {
    const timestamp = this.timestamps.get(key);
    
    if (!timestamp || Date.now() > timestamp) {
      // Cache expired or doesn't exist
      this.delete(key);
      return null;
    }
    
    return this.cache.get(key);
  }

  /**
   * Check if key exists and is not expired
   * @param {string} key - Cache key
   * @returns {boolean}
   */
  has(key) {
    const timestamp = this.timestamps.get(key);
    
    if (!timestamp || Date.now() > timestamp) {
      this.delete(key);
      return false;
    }
    
    return this.cache.has(key);
  }

  /**
   * Delete key from cache
   * @param {string} key - Cache key
   */
  delete(key) {
    this.cache.delete(key);
    this.timestamps.delete(key);
  }

  /**
   * Clear all cache
   */
  clear() {
    this.cache.clear();
    this.timestamps.clear();
  }

  /**
   * Get cache size
   * @returns {number}
   */
  size() {
    return this.cache.size;
  }

  /**
   * Clean expired entries
   */
  cleanup() {
    const now = Date.now();
    for (const [key, timestamp] of this.timestamps.entries()) {
      if (now > timestamp) {
        this.delete(key);
      }
    }
  }

  /**
   * Get cache statistics
   * @returns {object}
   */
  getStats() {
    const now = Date.now();
    let expired = 0;
    let active = 0;

    for (const timestamp of this.timestamps.values()) {
      if (now > timestamp) {
        expired++;
      } else {
        active++;
      }
    }

    return {
      total: this.cache.size,
      active,
      expired,
      memoryUsage: this.estimateMemoryUsage()
    };
  }

  /**
   * Estimate memory usage (rough calculation)
   * @returns {number} - Estimated memory usage in bytes
   */
  estimateMemoryUsage() {
    let size = 0;
    for (const [key, value] of this.cache.entries()) {
      size += key.length * 2; // Rough estimate for string key
      size += JSON.stringify(value).length * 2; // Rough estimate for value
    }
    return size;
  }
}

// Create singleton instance
const dataCache = new DataCache();

// Auto cleanup every 10 minutes
setInterval(() => {
  dataCache.cleanup();
}, 10 * 60 * 1000);

// Clear cache when database changes
if (typeof window !== 'undefined') {
  window.addEventListener('dbChange', () => {
    dataCache.clear();
    console.log('Data cache cleared due to database change');
  });
}

export default dataCache;

// Helper functions for common cache operations
export const getCachedData = async (key, fetchFunction, ttl) => {
  // Check cache first
  const cached = dataCache.get(key);
  if (cached !== null) {
    console.log(`Cache hit for key: ${key}`);
    return cached;
  }

  // Fetch fresh data
  console.log(`Cache miss for key: ${key}, fetching fresh data`);
  try {
    const data = await fetchFunction();
    dataCache.set(key, data, ttl);
    return data;
  } catch (error) {
    console.error(`Error fetching data for key ${key}:`, error);
    throw error;
  }
};

export const invalidateCache = (pattern) => {
  if (pattern) {
    // Delete keys matching pattern
    for (const key of dataCache.cache.keys()) {
      if (key.includes(pattern)) {
        dataCache.delete(key);
      }
    }
  } else {
    // Clear all cache
    dataCache.clear();
  }
};

export const getCacheStats = () => dataCache.getStats();
