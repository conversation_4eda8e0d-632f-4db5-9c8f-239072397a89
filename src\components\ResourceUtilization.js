// src/components/ResourceUtilization.js
import React, { useState, useEffect, useMemo, useCallback, memo } from "react";
import {
  Container, Box, Button, Table, TableHead, TableRow, TableCell,
  TableBody, TableContainer, TextField, Tooltip, Typography,
  Card, CardContent, IconButton, useTheme, TablePagination
} from "@mui/material";
import * as XLSX from "xlsx";
import { loadCostCenterData } from "../db";
import Autocomplete from "@mui/material/Autocomplete";
import Checkbox from "@mui/material/Checkbox";
import Chip from "@mui/material/Chip";
import { motion } from "framer-motion";
import TimelineIcon from "@mui/icons-material/Timeline";
import FileDownloadIcon from "@mui/icons-material/FileDownload";
import DeleteIcon from "@mui/icons-material/Delete";
import FilterListIcon from "@mui/icons-material/FilterList";
import ClearAllIcon from "@mui/icons-material/ClearAll";
import SearchIcon from "@mui/icons-material/Search";
// Import custom components
import PageHeader from "./common/PageHeader";
import TableSkeleton from "./common/TableSkeleton";
import NoDataPlaceholder from "./common/NoDataPlaceholder";
import { useToast } from "./common/ToastProvider";
import AnimationWrapper from "./common/AnimationWrapper";


const normalize = (s) => s ? s.trim().toLowerCase() : "";

const ResourceUtilization = () => {
  const theme = useTheme();
  const { showToast } = useToast();

  // State variables
  const [selectedMonth, setSelectedMonth] = useState([]);
  const [selectedEmployees, setSelectedEmployees] = useState([]);
  const [selectedCostCenters, setSelectedCostCenters] = useState([]);
  const [universalSearch, setUniversalSearch] = useState("");
  const [uniqueMonths, setUniqueMonths] = useState([]);
  const [uniqueEmployees, setUniqueEmployees] = useState([]);
  const [uniqueCostCenters, setUniqueCostCenters] = useState([]);
  const [computedData, setComputedData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedRows, setSelectedRows] = useState({});

  // Pagination
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(100);

  // Memoized function to fetch unique values
  const fetchUniqueValues = useCallback(async () => {
    const ccData = await loadCostCenterData();
    const months = ccData.filter(r => r.month && r.month.trim() !== "").map(r => r.month.trim());
    setUniqueMonths([...new Set(months)]);
    const empNames = ccData.filter(r => r.name && r.name.trim() !== "").map(r => r.name.trim());
    setUniqueEmployees([...new Set(empNames)]);
    const costCenters = ccData.filter(r => r.costCenter && r.costCenter.trim() !== "").map(r => r.costCenter.trim());
    setUniqueCostCenters([...new Set(costCenters)]);
  }, []);

  useEffect(() => {
    fetchUniqueValues();
    const handleDBChange = () => fetchUniqueValues();
    window.addEventListener("dbChange", handleDBChange);
    return () => window.removeEventListener("dbChange", handleDBChange);
  }, [fetchUniqueValues]);

  // Memoized computation function
  const computeUtilization = useCallback(async () => {
    if (selectedMonth.length === 0) {
      setComputedData([]);
      return;
    }

    setLoading(true);
    try {
      const ccData = await loadCostCenterData();
      const filtered = ccData.filter(r =>
        r.month && r.month.trim() && selectedMonth.includes(r.month.trim())
      );

      // Group by employee more efficiently
      const groups = {};
      filtered.forEach(r => {
        const emp = r.name ? r.name.trim() : "";
        if (!emp) return;
        if (!groups[emp]) groups[emp] = [];
        groups[emp].push(r);
      });

      const computed = Object.keys(groups).map(emp => {
        const records = groups[emp];
        const totalHours = records.reduce((sum, r) =>
          sum + (parseFloat(r.hoursWorked) || parseFloat(r.hours) || 0), 0);
        const utilization = {};
        records.forEach(r => {
          const cc = r.costCenter ? r.costCenter.trim() : "";
          const hrs = parseFloat(r.hoursWorked) || parseFloat(r.hours) || 0;
          utilization[cc] = (utilization[cc] || 0) + hrs;
        });
        return { employee: emp, utilization, totalHours };
      });

      setComputedData(computed);
    } catch (error) {
      console.error('Error computing utilization:', error);
      setComputedData([]);
    } finally {
      setLoading(false);
    }
  }, [selectedMonth]);

  useEffect(() => {
    computeUtilization();
    const handleDBChange = () => {
      if (selectedMonth.length > 0) computeUtilization();
    };
    window.addEventListener("dbChange", handleDBChange);
    return () => window.removeEventListener("dbChange", handleDBChange);
  }, [computeUtilization]);

  // Memoized filtered data to prevent unnecessary recalculations
  const filteredData = useMemo(() => {
    let filtered = computedData.filter(rec => {
      const search = universalSearch.toLowerCase();
      const empMatch = rec.employee.toLowerCase().includes(search);
      const ccMatch = Object.keys(rec.utilization).some(cc => cc.toLowerCase().includes(search));
      let base = empMatch || ccMatch;
      if (selectedEmployees.length > 0) {
        base = base && selectedEmployees.map(normalize).includes(rec.employee.toLowerCase());
      }
      return base;
    });

    if (selectedCostCenters.length > 0) {
      filtered = filtered.filter(rec =>
        selectedCostCenters.some(cc => (rec.utilization[cc] || 0) > 0)
      );
    }

    return filtered;
  }, [computedData, universalSearch, selectedEmployees, selectedCostCenters]);

  // Memoized final data with total hours calculation
  const finalData = useMemo(() => {
    return filteredData.map(rec => {
      const totalHours = uniqueCostCenters.reduce((sum, cc) => sum + (rec.utilization[cc] || 0), 0);
      return { ...rec, totalHours };
    });
  }, [filteredData, uniqueCostCenters]);

  // Memoized pagination handlers
  const handleChangePage = useCallback((event, newPage) => {
    setPage(newPage);
  }, []);

  const handleChangeRowsPerPage = useCallback((event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  }, []);

  // Memoized paginated data
  const paginatedData = useMemo(() => {
    return finalData.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);
  }, [finalData, page, rowsPerPage]);

  const handleSelectEntry = useCallback((employee) => {
    setSelectedRows(prev => ({ ...prev, [employee]: !prev[employee] }));
  }, []);

  const handleSelectAll = useCallback((checked) => {
    // Start with current selections
    const newSelections = { ...selectedRows };

    // Update selections for visible rows only (current page)
    paginatedData.forEach(rec => {
      newSelections[rec.employee] = checked;
    });

    setSelectedRows(newSelections);
  }, [selectedRows, paginatedData]);

  const handleDeleteSelected = useCallback(() => {
    const selectedCount = Object.values(selectedRows).filter(Boolean).length;
    if (selectedCount === 0) {
      showToast('Please select at least one row to delete', 'warning');
      return;
    }

    // Remove selected items from the main data source
    const remaining = computedData.filter(rec => !selectedRows[rec.employee]);
    setComputedData(remaining);
    setSelectedRows({});

    showToast(`Successfully deleted ${selectedCount} item${selectedCount !== 1 ? 's' : ''}`, 'success');
  }, [selectedRows, computedData, showToast]);

  const handleExport = useCallback(() => {
    if (finalData.length === 0) {
      showToast('No data to export', 'warning');
      return;
    }

    const displayColumns = selectedCostCenters.length > 0 ? selectedCostCenters : uniqueCostCenters;
    const header = ["Employee", ...displayColumns, "Total Hours"];
    const rows = finalData.map(rec => {
      const row = [rec.employee];
      let total = 0;
      displayColumns.forEach(cc => {
        const val = rec.utilization[cc] || 0;
        total += val;
        row.push(val.toFixed(2));
      });
      row.push(total.toFixed(2));
      return row;
    });

    // Add a total row
    const totalRow = ["Total"];
    let grandTotal = 0;
    displayColumns.forEach(cc => {
      const columnTotal = finalData.reduce((sum, row) => sum + (row.utilization[cc] || 0), 0);
      grandTotal += columnTotal;
      totalRow.push(columnTotal.toFixed(2));
    });
    totalRow.push(grandTotal.toFixed(2));

    const wsData = [header, ...rows, totalRow];
    const ws = XLSX.utils.aoa_to_sheet(wsData);

    // Add some styling to the header and total row
    const range = XLSX.utils.decode_range(ws['!ref']);
    for (let C = range.s.c; C <= range.e.c; ++C) {
      const headerCell = XLSX.utils.encode_cell({ r: 0, c: C });
      const totalCell = XLSX.utils.encode_cell({ r: rows.length + 1, c: C });

      if (!ws[headerCell]) continue;
      if (!ws[headerCell].s) ws[headerCell].s = {};
      ws[headerCell].s.font = { bold: true };

      if (!ws[totalCell]) continue;
      if (!ws[totalCell].s) ws[totalCell].s = {};
      ws[totalCell].s.font = { bold: true };
    }

    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "Utilization");
    XLSX.writeFile(wb, `ResourceUtilization_${selectedMonth.join(',')}.xlsx`);

    showToast('Data exported successfully', 'success');
  }, [finalData, selectedCostCenters, uniqueCostCenters, selectedMonth, showToast]);

  const handleClearFilters = useCallback(() => {
    setSelectedEmployees([]);
    setSelectedCostCenters([]);
    setUniversalSearch('');
    showToast('Filters cleared', 'info');
  }, [showToast]);


  const MonthDropdown = () => (
    <Autocomplete
      multiple
      value={selectedMonth}
      onChange={(_, newValue) => setSelectedMonth(newValue)}
      options={uniqueMonths}
      getOptionLabel={option => option}
      id="month-dropdown"
      renderInput={params => (
        <TextField
          {...params}
          label="Select Months"
          placeholder="Select months..."
          fullWidth
          InputProps={{
            ...params.InputProps,
            startAdornment: (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                {params.InputProps.startAdornment}
                <Tooltip title={selectedMonth.length === uniqueMonths.length ? "Deselect all months" : "Select all months"}>
                  <Button
                    size="small"
                    onClick={() => {
                      if (selectedMonth.length === uniqueMonths.length) setSelectedMonth([]);
                      else setSelectedMonth(uniqueMonths);
                    }}
                    sx={{ color: theme.palette.primary.main }}
                  >
                    {selectedMonth.length === uniqueMonths.length ? 'Deselect All' : 'Select All'}
                  </Button>
                </Tooltip>
              </Box>
            )
          }}
        />
      )}
      renderOption={(props, option, { selected }) => (
        <motion.li
          {...props}
          whileHover={{ backgroundColor: theme.palette.action.hover }}
          transition={{ duration: 0.1 }}
        >
          <Checkbox style={{ marginRight: 8 }} checked={selected} />
          {option}
        </motion.li>
      )}
      renderTags={(value, getTagProps) =>
        value.map((option, index) => (
          <Chip
            variant="outlined"
            label={option}
            {...getTagProps({ index })}
            size="small"
            sx={{
              borderColor: theme.palette.primary.main,
              color: theme.palette.primary.main,
              '&:hover': {
                backgroundColor: `${theme.palette.primary.light}10`,
              },
              transition: 'all 0.2s ease-in-out',
            }}
          />
        ))
      }
      disableCloseOnSelect
      ListboxProps={{
        style: { maxHeight: '250px' }
      }}
    />
  );

  const EmployeeDropdown = () => (
    <Autocomplete
      multiple
      value={selectedEmployees}
      onChange={(_, newValue) => setSelectedEmployees(newValue)}
      options={uniqueEmployees}
      getOptionLabel={option => option}
      id="employee-dropdown"
      renderInput={params => (
        <TextField
          {...params}
          label="Select Employees"
          placeholder="Select employees..."
          fullWidth
          InputProps={{
            ...params.InputProps,
            startAdornment: (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                {params.InputProps.startAdornment}
                <Tooltip title={selectedEmployees.length === uniqueEmployees.length ? "Deselect all employees" : "Select all employees"}>
                  <Button
                    size="small"
                    onClick={() => {
                      if (selectedEmployees.length === uniqueEmployees.length) setSelectedEmployees([]);
                      else setSelectedEmployees(uniqueEmployees);
                    }}
                    sx={{ color: theme.palette.primary.main }}
                  >
                    {selectedEmployees.length === uniqueEmployees.length ? 'Deselect All' : 'Select All'}
                  </Button>
                </Tooltip>
              </Box>
            )
          }}
        />
      )}
      renderOption={(props, option, { selected }) => (
        <motion.li
          {...props}
          whileHover={{ backgroundColor: theme.palette.action.hover }}
          transition={{ duration: 0.1 }}
        >
          <Checkbox style={{ marginRight: 8 }} checked={selected} />
          {option}
        </motion.li>
      )}
      renderTags={(value, getTagProps) =>
        value.map((option, index) => (
          <Chip
            variant="outlined"
            label={option}
            {...getTagProps({ index })}
            size="small"
            sx={{
              borderColor: theme.palette.primary.main,
              color: theme.palette.primary.main,
              '&:hover': {
                backgroundColor: `${theme.palette.primary.light}10`,
              },
              transition: 'all 0.2s ease-in-out',
            }}
          />
        ))
      }
      disableCloseOnSelect
      ListboxProps={{
        style: { maxHeight: '250px' }
      }}
    />
  );

  const CostCenterDropdown = () => (
    <Autocomplete
      multiple
      value={selectedCostCenters}
      onChange={(_, newValue) => setSelectedCostCenters(newValue)}
      options={uniqueCostCenters}
      getOptionLabel={option => option}
      id="cost-center-dropdown"
      renderInput={params => (
        <TextField
          {...params}
          label="Select Cost Centers"
          placeholder="Select cost centers..."
          fullWidth
          InputProps={{
            ...params.InputProps,
            startAdornment: (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                {params.InputProps.startAdornment}
                <Tooltip title={selectedCostCenters.length === uniqueCostCenters.length ? "Deselect all cost centers" : "Select all cost centers"}>
                  <Button
                    size="small"
                    onClick={() => {
                      if (selectedCostCenters.length === uniqueCostCenters.length) setSelectedCostCenters([]);
                      else setSelectedCostCenters(uniqueCostCenters);
                    }}
                    sx={{ color: theme.palette.primary.main }}
                  >
                    {selectedCostCenters.length === uniqueCostCenters.length ? 'Deselect All' : 'Select All'}
                  </Button>
                </Tooltip>
              </Box>
            )
          }}
        />
      )}
      renderOption={(props, option, { selected }) => (
        <motion.li
          {...props}
          whileHover={{ backgroundColor: theme.palette.action.hover }}
          transition={{ duration: 0.1 }}
        >
          <Checkbox style={{ marginRight: 8 }} checked={selected} />
          {option}
        </motion.li>
      )}
      renderTags={(value, getTagProps) =>
        value.map((option, index) => (
          <Chip
            variant="outlined"
            label={option}
            {...getTagProps({ index })}
            size="small"
            sx={{
              borderColor: theme.palette.primary.main,
              color: theme.palette.primary.main,
              '&:hover': {
                backgroundColor: `${theme.palette.primary.light}10`,
              },
              transition: 'all 0.2s ease-in-out',
            }}
          />
        ))
      }
      disableCloseOnSelect
      ListboxProps={{
        style: { maxHeight: '250px' }
      }}
    />
  );

  // Memoized page actions for the header
  const pageActions = useMemo(() => [
    {
      label: 'Export to Excel',
      onClick: handleExport,
      icon: <FileDownloadIcon />,
      variant: 'contained',
      color: 'primary',
    },
    {
      label: 'Delete Selected',
      onClick: handleDeleteSelected,
      icon: <DeleteIcon />,
      variant: 'contained',
      color: 'error',
      disabled: Object.values(selectedRows).filter(Boolean).length === 0,
    },
  ], [handleExport, handleDeleteSelected, selectedRows]);

  return (
    <AnimationWrapper type="fadeIn">
      <Container maxWidth="xl" sx={{ py: 3 }}>
        {/* Page Header */}
        <PageHeader
          title="Resource Utilization"
          subtitle="View and analyze resource utilization across different cost centers"
          icon={TimelineIcon}
          actions={pageActions}
        />

        {/* Filters Section */}
        <Card sx={{ mb: 3, p: 2 }}>
          <CardContent>
            <Box sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
              <FilterListIcon sx={{ mr: 1, color: theme.palette.primary.main }} />
              <Typography variant="h6">Filters</Typography>
            </Box>

            <Box sx={{ mb: 3 }}>
              <TextField
                label="Search"
                variant="outlined"
                fullWidth
                value={universalSearch}
                onChange={(e) => setUniversalSearch(e.target.value)}
                placeholder="Search by employee or cost center..."
                InputProps={{
                  startAdornment: <SearchIcon sx={{ color: 'action.active', mr: 1 }} />,
                  endAdornment: universalSearch ? (
                    <IconButton
                      size="small"
                      onClick={() => setUniversalSearch('')}
                      edge="end"
                    >
                      <ClearAllIcon fontSize="small" />
                    </IconButton>
                  ) : null,
                }}
                sx={{ mb: 2 }}
              />
            </Box>

            <Box sx={{ mb: 3 }}>
              <MonthDropdown />
            </Box>

            <Box sx={{ mb: 3 }}>
              <EmployeeDropdown />
            </Box>

            <Box sx={{ mb: 3 }}>
              <CostCenterDropdown />
            </Box>

            <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
              <Button
                variant="outlined"
                onClick={handleClearFilters}
                startIcon={<ClearAllIcon />}
              >
                Clear All Filters
              </Button>
            </Box>
          </CardContent>
        </Card>

        {/* Data Table Section */}
        {loading ? (
          <TableSkeleton
            rows={5}
            columns={uniqueCostCenters.length + 2}
            showTableHeader
          />
        ) : finalData.length === 0 ? (
          <NoDataPlaceholder
            type={selectedMonth.length === 0 ? 'empty' : 'noResults'}
            primaryAction={{
              label: 'Select Month',
              onClick: () => document.getElementById('month-dropdown').focus(),
            }}
            secondaryAction={{
              label: 'Clear Filters',
              onClick: handleClearFilters,
            }}
          />
        ) : (
          <Card sx={{ mt: 3, overflow: 'hidden' }}>
            <Box sx={{ p: 2, display: 'flex', justifyContent: 'flex-end', alignItems: 'center', borderBottom: `1px solid ${theme.palette.divider}` }}>
              <Typography variant="caption" color="text.secondary" sx={{ display: 'flex', alignItems: 'center' }}>
                <span style={{ marginRight: '4px' }}>Scroll horizontally to view all columns</span>
                <span style={{ fontSize: '16px' }}>↔️</span>
              </Typography>
            </Box>
            <CardContent sx={{ p: 0 }}>
              <TableContainer sx={{ maxWidth: '100%', overflowX: 'auto', height: '600px' }}>
                <Table stickyHeader size="small" sx={{ minWidth: uniqueCostCenters.length > 5 ? (uniqueCostCenters.length * 150) + 300 : '100%' }}>
                  <TableHead>
                    <TableRow>
                      <TableCell padding="checkbox" sx={{
                        position: 'sticky',
                        left: 0,
                        top: 0,
                        zIndex: 4,
                        backgroundColor: theme.palette.background.default,
                        borderBottom: `2px solid ${theme.palette.primary.main}`,
                      }}>
                        <Tooltip title="Select All Visible Rows">
                          <Checkbox
                            checked={paginatedData.length > 0 && paginatedData.every(row => selectedRows[row.employee])}
                            onChange={(e) => handleSelectAll(e.target.checked)}
                            indeterminate={
                              paginatedData.some(row => selectedRows[row.employee]) &&
                              !paginatedData.every(row => selectedRows[row.employee])
                            }
                          />
                        </Tooltip>
                      </TableCell>
                      <TableCell sx={{
                        fontWeight: "bold",
                        position: 'sticky',
                        left: 57,
                        top: 0,
                        zIndex: 4,
                        backgroundColor: theme.palette.background.default,
                        borderBottom: `2px solid ${theme.palette.primary.main}`,
                        boxShadow: '2px 0 4px -2px rgba(0,0,0,0.15)'
                      }}>Employee</TableCell>
                      {uniqueCostCenters.map((cc, idx) => (
                        <TableCell key={idx} sx={{
                          fontWeight: "bold",
                          position: 'sticky',
                          top: 0,
                          zIndex: 3,
                          backgroundColor: theme.palette.background.default,
                          borderBottom: `2px solid ${theme.palette.primary.main}`,
                        }}>
                          <Tooltip title={`Hours worked for ${cc}`} placement="top">
                            <span>{cc} Hours</span>
                          </Tooltip>
                        </TableCell>
                      ))}
                      <TableCell sx={{
                        fontWeight: "bold",
                        position: 'sticky',
                        top: 0,
                        zIndex: 3,
                        backgroundColor: theme.palette.background.default,
                        borderBottom: `2px solid ${theme.palette.primary.main}`,
                      }}>Total Hours</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {paginatedData.map((row, index) => (
                      <TableRow
                        key={row.employee}
                        hover
                        sx={{
                          backgroundColor: index % 2 === 0 ? 'inherit' : theme.palette.grey[50],
                        }}
                      >
                        <TableCell padding="checkbox" sx={{ position: 'sticky', left: 0, zIndex: 2, backgroundColor: index % 2 === 0 ? theme.palette.background.paper : theme.palette.grey[50] }}>
                          <Checkbox
                            checked={!!selectedRows[row.employee]}
                            onChange={() => handleSelectEntry(row.employee)}
                          />
                        </TableCell>
                        <TableCell sx={{
                          position: 'sticky',
                          left: 57,
                          zIndex: 2,
                          backgroundColor: index % 2 === 0 ? theme.palette.background.paper : theme.palette.grey[50],
                          boxShadow: '2px 0 4px -2px rgba(0,0,0,0.15)'
                        }}>{row.employee}</TableCell>
                        {uniqueCostCenters.map((cc, i) => (
                          <TableCell key={i}>
                            {row.utilization[cc] ? row.utilization[cc].toFixed(2) : ""}
                          </TableCell>
                        ))}
                        <TableCell sx={{ fontWeight: 'medium' }}>
                          {row.totalHours.toFixed(2)}
                        </TableCell>
                      </TableRow>
                    ))}
                    {/* Total Row */}
                    <TableRow sx={{
                      backgroundColor: theme.palette.primary.light + '20',
                      '& td': { fontWeight: 'bold' }
                    }}>
                      <TableCell padding="checkbox" sx={{ position: 'sticky', left: 0, zIndex: 2, backgroundColor: theme.palette.primary.light + '20' }} />
                      <TableCell sx={{
                        position: 'sticky',
                        left: 57,
                        zIndex: 2,
                        backgroundColor: theme.palette.primary.light + '20',
                        boxShadow: '2px 0 4px -2px rgba(0,0,0,0.15)'
                      }}>Total</TableCell>
                      {uniqueCostCenters.map((cc, i) => {
                        const total = finalData.reduce((sum, row) => sum + (row.utilization[cc] || 0), 0);
                        return (
                          <TableCell key={i}>
                            {total.toFixed(2)}
                          </TableCell>
                        );
                      })}
                      <TableCell>
                        {finalData.reduce((sum, row) => sum + row.totalHours, 0).toFixed(2)}
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
              <TablePagination
                rowsPerPageOptions={[25, 50, 100, 200]}
                component="div"
                count={finalData.length}
                rowsPerPage={rowsPerPage}
                page={page}
                onPageChange={handleChangePage}
                onRowsPerPageChange={handleChangeRowsPerPage}
                sx={{ borderTop: `1px solid ${theme.palette.divider}` }}
              />
            </CardContent>
          </Card>
        )}
      </Container>
    </AnimationWrapper>
  );
};

export default memo(ResourceUtilization);
