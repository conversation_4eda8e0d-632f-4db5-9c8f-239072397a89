// Utility functions for cost center mappings

/**
 * Apply cost center mappings to a cost center name (case-insensitive matching)
 * @param {string} originalName - The original cost center name
 * @param {Object} mappings - Object with originalName as key and mapping object {alternateName, categoryName} as value
 * @returns {string} - The alternate name if mapping exists, otherwise the original name
 */
export const applyCostCenterMapping = (originalName, mappings = {}) => {
  if (!originalName || typeof originalName !== 'string') {
    return originalName || '';
  }

  const trimmedName = originalName.trim();

  // First try exact match (for backward compatibility)
  if (mappings[trimmedName]) {
    const mapping = mappings[trimmedName];
    // Handle both old format (string) and new format (object)
    if (typeof mapping === 'string') {
      return mapping;
    } else if (mapping && mapping.alternateName) {
      return mapping.alternateName;
    }
  }

  // Then try case-insensitive match
  const lowerTrimmed = trimmedName.toLowerCase();
  for (const [key, value] of Object.entries(mappings)) {
    if (key.toLowerCase() === lowerTrimmed) {
      // Handle both old format (string) and new format (object)
      if (typeof value === 'string') {
        return value;
      } else if (value && value.alternateName) {
        return value.alternateName;
      }
    }
  }

  // Return original if no mapping found
  return trimmedName;
};

/**
 * Get category name for a cost center (case-insensitive matching)
 * @param {string} originalName - The original cost center name
 * @param {Object} mappings - Object with originalName as key and mapping object {alternateName, categoryName} as value
 * @returns {string} - The category name if mapping exists, otherwise empty string
 */
export const getCostCenterCategoryName = (originalName, mappings = {}) => {
  if (!originalName || typeof originalName !== 'string') {
    return '';
  }

  const trimmedName = originalName.trim();

  // First try exact match
  if (mappings[trimmedName]) {
    const mapping = mappings[trimmedName];
    // Handle new format (object)
    if (mapping && typeof mapping === 'object' && mapping.categoryName) {
      return mapping.categoryName;
    }
  }

  // Then try case-insensitive match
  const lowerTrimmed = trimmedName.toLowerCase();
  for (const [key, value] of Object.entries(mappings)) {
    if (key.toLowerCase() === lowerTrimmed) {
      // Handle new format (object)
      if (value && typeof value === 'object' && value.categoryName) {
        return value.categoryName;
      }
    }
  }

  // Return empty string if no category mapping found
  return '';
};

/**
 * Get ledger name for a cost center (case-insensitive matching)
 * @param {string} originalName - The original cost center name
 * @param {Object} mappings - Object with originalName as key and mapping object {alternateName, categoryName, ledgerName} as value
 * @returns {string} - The ledger name if mapping exists, otherwise empty string
 */
export const getCostCenterLedgerName = (originalName, mappings = {}) => {
  if (!originalName || typeof originalName !== 'string') {
    return '';
  }

  const trimmedName = originalName.trim();

  // First try exact match
  if (mappings[trimmedName]) {
    const mapping = mappings[trimmedName];
    // Handle new format (object)
    if (mapping && typeof mapping === 'object' && mapping.ledgerName) {
      return mapping.ledgerName;
    }
  }

  // Then try case-insensitive match
  const lowerTrimmed = trimmedName.toLowerCase();
  for (const [key, value] of Object.entries(mappings)) {
    if (key.toLowerCase() === lowerTrimmed) {
      // Handle new format (object)
      if (value && typeof value === 'object' && value.ledgerName) {
        return value.ledgerName;
      }
    }
  }

  // Return empty string if no ledger mapping found
  return '';
};

/**
 * Apply cost center mappings to an array of data objects
 * @param {Array} data - Array of objects containing cost center data
 * @param {Object} mappings - Object with originalName as key and alternateName as value
 * @param {string} costCenterField - The field name that contains the cost center (default: 'costCenter')
 * @returns {Array} - Array with mapped cost center names
 */
export const applyCostCenterMappingsToData = (data, mappings = {}, costCenterField = 'costCenter') => {
  if (!Array.isArray(data)) {
    return data;
  }

  return data.map(item => ({
    ...item,
    [costCenterField]: applyCostCenterMapping(item[costCenterField], mappings),
    // Keep original for reference if needed
    [`original${costCenterField.charAt(0).toUpperCase() + costCenterField.slice(1)}`]: item[costCenterField],
  }));
};

// Cache for unique cost centers to avoid repeated calculations
const uniqueCostCentersCache = new Map();

/**
 * Get unique cost centers from data array (case-insensitive) with caching
 * @param {Array} data - Array of objects containing cost center data
 * @param {string} costCenterField - The field name that contains the cost center (default: 'costCenter')
 * @returns {Array} - Array of unique cost center names with preserved casing
 */
export const getUniqueCostCenters = (data, costCenterField = 'costCenter') => {
  if (!Array.isArray(data)) {
    console.warn('getUniqueCostCenters: data is not an array', data);
    return [];
  }

  // Create cache key based on data length and first/last items
  const cacheKey = `${costCenterField}-${data.length}-${JSON.stringify(data[0] || {})}-${JSON.stringify(data[data.length - 1] || {})}`;

  // Check cache first
  if (uniqueCostCentersCache.has(cacheKey)) {
    console.log(`getUniqueCostCenters: Cache hit for ${data.length} records`);
    return uniqueCostCentersCache.get(cacheKey);
  }

  console.log(`getUniqueCostCenters: Processing ${data.length} records`);

  // Use Set for better performance with large datasets
  const costCenterSet = new Set();
  const caseMap = new Map(); // Track preferred casing

  data.forEach((item) => {
    const costCenter = item[costCenterField];

    if (!costCenter || typeof costCenter !== 'string') {
      return; // Skip invalid entries
    }

    const trimmed = costCenter.trim();
    if (trimmed === '') {
      return; // Skip empty entries
    }

    const lowerKey = trimmed.toLowerCase();

    if (!caseMap.has(lowerKey)) {
      caseMap.set(lowerKey, trimmed);
      costCenterSet.add(trimmed);
    }
  });

  // Convert to sorted array
  const uniqueCostCenters = Array.from(costCenterSet)
    .sort((a, b) => a.localeCompare(b, undefined, { sensitivity: 'base' }));

  // Cache the result
  uniqueCostCentersCache.set(cacheKey, uniqueCostCenters);

  // Limit cache size to prevent memory issues
  if (uniqueCostCentersCache.size > 10) {
    const firstKey = uniqueCostCentersCache.keys().next().value;
    uniqueCostCentersCache.delete(firstKey);
  }

  console.log(`getUniqueCostCenters: Found ${uniqueCostCenters.length} unique cost centers`);
  if (uniqueCostCenters.length <= 20) {
    console.log('getUniqueCostCenters: All unique cost centers:', uniqueCostCenters);
  } else {
    console.log('getUniqueCostCenters: First 10:', uniqueCostCenters.slice(0, 10));
    console.log('getUniqueCostCenters: Last 10:', uniqueCostCenters.slice(-10));
  }

  return uniqueCostCenters;
};

/**
 * Create a display name for cost center that shows both original and alternate if different
 * @param {string} originalName - The original cost center name
 * @param {Object} mappings - Object with originalName as key and mapping object {alternateName, categoryName} as value
 * @param {boolean} showBoth - Whether to show both names when they differ (default: false)
 * @returns {string} - The display name
 */
export const getCostCenterDisplayName = (originalName, mappings = {}, showBoth = false) => {
  if (!originalName || typeof originalName !== 'string') {
    return originalName || '';
  }

  const trimmedName = originalName.trim();
  const mapping = mappings[trimmedName];

  let alternateName = '';
  if (mapping) {
    // Handle both old format (string) and new format (object)
    if (typeof mapping === 'string') {
      alternateName = mapping;
    } else if (mapping && mapping.alternateName) {
      alternateName = mapping.alternateName;
    }
  }

  if (!alternateName || alternateName === trimmedName) {
    return trimmedName;
  }

  if (showBoth) {
    return `${alternateName} (${trimmedName})`;
  }

  return alternateName;
};

/**
 * Validate cost center mappings
 * @param {Array} mappings - Array of mapping objects with originalName and alternateName
 * @returns {Object} - Validation result with isValid boolean and errors array
 */
export const validateCostCenterMappings = (mappings) => {
  const errors = [];
  const originalNames = new Set();
  const alternateNames = new Set();

  if (!Array.isArray(mappings)) {
    return { isValid: false, errors: ['Mappings must be an array'] };
  }

  mappings.forEach((mapping, index) => {
    const { originalName, alternateName } = mapping;

    // Check required fields
    if (!originalName || typeof originalName !== 'string' || originalName.trim() === '') {
      errors.push(`Mapping ${index + 1}: Original name is required`);
    }

    if (!alternateName || typeof alternateName !== 'string' || alternateName.trim() === '') {
      errors.push(`Mapping ${index + 1}: Alternate name is required`);
    }

    if (originalName && alternateName) {
      const trimmedOriginal = originalName.trim();
      const trimmedAlternate = alternateName.trim();

      // Check for duplicate original names
      if (originalNames.has(trimmedOriginal)) {
        errors.push(`Mapping ${index + 1}: Duplicate original name '${trimmedOriginal}'`);
      } else {
        originalNames.add(trimmedOriginal);
      }

      // Check for duplicate alternate names
      if (alternateNames.has(trimmedAlternate)) {
        errors.push(`Mapping ${index + 1}: Duplicate alternate name '${trimmedAlternate}'`);
      } else {
        alternateNames.add(trimmedAlternate);
      }

      // Check if original and alternate are the same
      if (trimmedOriginal === trimmedAlternate) {
        errors.push(`Mapping ${index + 1}: Original and alternate names cannot be the same`);
      }
    }
  });

  return {
    isValid: errors.length === 0,
    errors
  };
};
