import { useState, useEffect, useCallback, useRef } from 'react';

/**
 * Custom hook for debouncing values
 * @param {any} value - Value to debounce
 * @param {number} delay - Delay in milliseconds
 * @returns {any} - Debounced value
 */
export const useDebounce = (value, delay) => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

/**
 * Custom hook for debounced search functionality
 * @param {function} searchFunction - Function to call for search
 * @param {number} delay - Delay in milliseconds (default: 300)
 * @returns {object} - Search state and functions
 */
export const useDebouncedSearch = (searchFunction, delay = 300) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [searchResults, setSearchResults] = useState([]);
  const [error, setError] = useState(null);
  
  const debouncedSearchTerm = useDebounce(searchTerm, delay);
  const searchRef = useRef();

  const performSearch = useCallback(async (term) => {
    if (!term.trim()) {
      setSearchResults([]);
      setIsSearching(false);
      return;
    }

    setIsSearching(true);
    setError(null);

    try {
      const results = await searchFunction(term);
      setSearchResults(results);
    } catch (err) {
      setError(err);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  }, [searchFunction]);

  useEffect(() => {
    performSearch(debouncedSearchTerm);
  }, [debouncedSearchTerm, performSearch]);

  const clearSearch = useCallback(() => {
    setSearchTerm('');
    setSearchResults([]);
    setError(null);
  }, []);

  return {
    searchTerm,
    setSearchTerm,
    searchResults,
    isSearching,
    error,
    clearSearch
  };
};

/**
 * Custom hook for debounced callbacks
 * @param {function} callback - Callback function to debounce
 * @param {number} delay - Delay in milliseconds
 * @param {array} deps - Dependencies array
 * @returns {function} - Debounced callback
 */
export const useDebouncedCallback = (callback, delay, deps = []) => {
  const timeoutRef = useRef();

  const debouncedCallback = useCallback((...args) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      callback(...args);
    }, delay);
  }, [callback, delay, ...deps]);

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return debouncedCallback;
};

/**
 * Custom hook for throttling values
 * @param {any} value - Value to throttle
 * @param {number} limit - Time limit in milliseconds
 * @returns {any} - Throttled value
 */
export const useThrottle = (value, limit) => {
  const [throttledValue, setThrottledValue] = useState(value);
  const lastRan = useRef(Date.now());

  useEffect(() => {
    const handler = setTimeout(() => {
      if (Date.now() - lastRan.current >= limit) {
        setThrottledValue(value);
        lastRan.current = Date.now();
      }
    }, limit - (Date.now() - lastRan.current));

    return () => {
      clearTimeout(handler);
    };
  }, [value, limit]);

  return throttledValue;
};

/**
 * Custom hook for throttled callbacks
 * @param {function} callback - Callback function to throttle
 * @param {number} limit - Time limit in milliseconds
 * @param {array} deps - Dependencies array
 * @returns {function} - Throttled callback
 */
export const useThrottledCallback = (callback, limit, deps = []) => {
  const lastRan = useRef(Date.now());

  const throttledCallback = useCallback((...args) => {
    if (Date.now() - lastRan.current >= limit) {
      callback(...args);
      lastRan.current = Date.now();
    }
  }, [callback, limit, ...deps]);

  return throttledCallback;
};

export default useDebounce;
