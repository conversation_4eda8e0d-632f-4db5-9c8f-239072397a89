// Performance monitoring utility
class PerformanceMonitor {
  constructor() {
    this.metrics = new Map();
    this.isEnabled = process.env.NODE_ENV === 'development';
  }

  /**
   * Start timing an operation
   * @param {string} name - Operation name
   * @returns {function} - Function to end timing
   */
  startTiming(name) {
    if (!this.isEnabled) return () => {};

    const startTime = performance.now();
    const startMemory = this.getMemoryUsage();

    return () => {
      const endTime = performance.now();
      const endMemory = this.getMemoryUsage();
      const duration = endTime - startTime;
      const memoryDelta = endMemory - startMemory;

      this.recordMetric(name, {
        duration,
        memoryDelta,
        timestamp: Date.now()
      });

      console.log(`⚡ ${name}: ${duration.toFixed(2)}ms, Memory: ${memoryDelta > 0 ? '+' : ''}${memoryDelta.toFixed(2)}MB`);
    };
  }

  /**
   * Time an async operation
   * @param {string} name - Operation name
   * @param {function} operation - Async function to time
   * @returns {any} - Result of the operation
   */
  async timeAsync(name, operation) {
    const endTiming = this.startTiming(name);
    try {
      const result = await operation();
      endTiming();
      return result;
    } catch (error) {
      endTiming();
      throw error;
    }
  }

  /**
   * Time a synchronous operation
   * @param {string} name - Operation name
   * @param {function} operation - Function to time
   * @returns {any} - Result of the operation
   */
  timeSync(name, operation) {
    const endTiming = this.startTiming(name);
    try {
      const result = operation();
      endTiming();
      return result;
    } catch (error) {
      endTiming();
      throw error;
    }
  }

  /**
   * Record a metric
   * @param {string} name - Metric name
   * @param {object} data - Metric data
   */
  recordMetric(name, data) {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }
    
    const metrics = this.metrics.get(name);
    metrics.push(data);
    
    // Keep only last 100 measurements per metric
    if (metrics.length > 100) {
      metrics.shift();
    }
  }

  /**
   * Get memory usage in MB
   * @returns {number}
   */
  getMemoryUsage() {
    if (performance.memory) {
      return performance.memory.usedJSHeapSize / 1024 / 1024;
    }
    return 0;
  }

  /**
   * Get statistics for a metric
   * @param {string} name - Metric name
   * @returns {object|null}
   */
  getMetricStats(name) {
    const metrics = this.metrics.get(name);
    if (!metrics || metrics.length === 0) return null;

    const durations = metrics.map(m => m.duration);
    const memoryDeltas = metrics.map(m => m.memoryDelta);

    return {
      count: metrics.length,
      duration: {
        min: Math.min(...durations),
        max: Math.max(...durations),
        avg: durations.reduce((a, b) => a + b, 0) / durations.length,
        latest: durations[durations.length - 1]
      },
      memory: {
        min: Math.min(...memoryDeltas),
        max: Math.max(...memoryDeltas),
        avg: memoryDeltas.reduce((a, b) => a + b, 0) / memoryDeltas.length,
        latest: memoryDeltas[memoryDeltas.length - 1]
      }
    };
  }

  /**
   * Get all metrics
   * @returns {object}
   */
  getAllMetrics() {
    const result = {};
    for (const [name] of this.metrics) {
      result[name] = this.getMetricStats(name);
    }
    return result;
  }

  /**
   * Clear all metrics
   */
  clearMetrics() {
    this.metrics.clear();
  }

  /**
   * Log performance summary
   */
  logSummary() {
    if (!this.isEnabled) return;

    console.group('📊 Performance Summary');
    
    const allMetrics = this.getAllMetrics();
    for (const [name, stats] of Object.entries(allMetrics)) {
      if (stats) {
        console.log(`${name}:`, {
          calls: stats.count,
          avgDuration: `${stats.duration.avg.toFixed(2)}ms`,
          avgMemory: `${stats.memory.avg.toFixed(2)}MB`
        });
      }
    }
    
    console.groupEnd();
  }

  /**
   * Monitor React component render performance
   * @param {string} componentName - Component name
   * @returns {object} - Hooks for component lifecycle
   */
  monitorComponent(componentName) {
    let renderStart;
    
    return {
      onRenderStart: () => {
        renderStart = performance.now();
      },
      onRenderEnd: () => {
        if (renderStart) {
          const duration = performance.now() - renderStart;
          this.recordMetric(`${componentName}-render`, {
            duration,
            memoryDelta: 0,
            timestamp: Date.now()
          });
          
          if (duration > 16) { // Longer than one frame
            console.warn(`🐌 Slow render: ${componentName} took ${duration.toFixed(2)}ms`);
          }
        }
      }
    };
  }

  /**
   * Monitor data processing operations
   * @param {string} operationName - Operation name
   * @param {number} dataSize - Size of data being processed
   * @returns {function} - Function to end monitoring
   */
  monitorDataProcessing(operationName, dataSize) {
    const endTiming = this.startTiming(`${operationName}-${dataSize}-items`);
    
    return () => {
      endTiming();
      
      // Log warning for slow data processing
      const stats = this.getMetricStats(`${operationName}-${dataSize}-items`);
      if (stats && stats.duration.latest > 100) {
        console.warn(`🐌 Slow data processing: ${operationName} with ${dataSize} items took ${stats.duration.latest.toFixed(2)}ms`);
      }
    };
  }
}

// Create singleton instance
const performanceMonitor = new PerformanceMonitor();

// Auto-log summary every 30 seconds in development
if (process.env.NODE_ENV === 'development') {
  setInterval(() => {
    performanceMonitor.logSummary();
  }, 30000);
}

export default performanceMonitor;

// Helper functions
export const timeAsync = (name, operation) => performanceMonitor.timeAsync(name, operation);
export const timeSync = (name, operation) => performanceMonitor.timeSync(name, operation);
export const startTiming = (name) => performanceMonitor.startTiming(name);
export const monitorComponent = (name) => performanceMonitor.monitorComponent(name);
export const monitorDataProcessing = (name, size) => performanceMonitor.monitorDataProcessing(name, size);
