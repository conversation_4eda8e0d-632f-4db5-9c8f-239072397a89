// src/components/Tally.js
import React, { useState, useEffect, useMemo, useCallback, memo } from "react";
import {
  Container,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Button,
  CircularProgress,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  TableContainer,
  Paper,
  Card,
  CardContent,
  IconButton,
  Tooltip,
} from "@mui/material";
import { Settings as SettingsIcon } from "@mui/icons-material";
import { motion } from "framer-motion";
import * as XLSX from "xlsx";
import { loadCostCenterData, loadSalaryData, loadCostCenterMappings } from "../db";
import CostCenterMappingModal from "./CostCenterMappingModal";
import { applyCostCenterMapping, getUniqueCostCenters } from "../utils/costCenterMappings";

const Tally = ({ selectedMonth }) => {
  // Local state
  const [month, setMonth] = useState(selectedMonth || "");
  const [uniqueMonths, setUniqueMonths] = useState([]);
  const [aggregatedData, setAggregatedData] = useState([]);
  const [differenceData, setDifferenceData] = useState([]);
  const [totals, setTotals] = useState({ ccGross: 0, ccPF: 0, salaryGross: 0, salaryPF: 0 });
  const [loading, setLoading] = useState(false);

  // Cost center mapping state
  const [costCenterMappings, setCostCenterMappings] = useState({});
  const [mappingModalOpen, setMappingModalOpen] = useState(false);
  const [uniqueCostCenters, setUniqueCostCenters] = useState([]);

  // When parent passes a selectedMonth, update local state
  useEffect(() => {
    setMonth(selectedMonth);
  }, [selectedMonth]);

  // Memoized function to load cost center mappings
  const loadMappings = useCallback(async () => {
    try {
      const mappings = await loadCostCenterMappings();
      const mappingObject = {};
      mappings.forEach(mapping => {
        mappingObject[mapping.originalName] = {
          alternateName: mapping.alternateName,
          categoryName: mapping.categoryName || '',
          ledgerName: mapping.ledgerName || ''
        };
      });
      setCostCenterMappings(mappingObject);
    } catch (error) {
      console.error('Error loading cost center mappings:', error);
    }
  }, []);

  // Load cost center mappings
  useEffect(() => {
    loadMappings();

    // Listen for database changes to reload mappings
    const handleDBChange = () => loadMappings();
    window.addEventListener("dbChange", handleDBChange);
    return () => window.removeEventListener("dbChange", handleDBChange);
  }, [loadMappings]);

  // Memoized function to fetch unique months and cost centers
  const fetchUniqueMonths = useCallback(async () => {
    try {
      console.log('Tally: Loading cost center data...');
      const ccData = await loadCostCenterData();
      console.log(`Tally: Loaded ${ccData.length} cost center records`);

      if (ccData.length === 0) {
        console.warn('Tally: No cost center data found! This might be why only 4 cost centers are showing.');
        setUniqueCostCenters([]);
        setUniqueMonths([]);
        return;
      }

      const months = ccData
        .filter(record => record.month && record.month.trim() !== "")
        .map(record => record.month.trim());
      const unique = [...new Set(months)];
      setUniqueMonths(unique);
      console.log(`Tally: Found ${unique.length} unique months:`, unique);

      // Extract ALL unique cost centers from the entire dataset for the mapping modal
      console.log('Tally: Extracting unique cost centers...');
      console.log(`Tally: Total cost center records loaded: ${ccData.length}`);

      // Debug: Log first few records to see the data structure
      console.log('Tally: Sample cost center records:', ccData.slice(0, 5));

      // Debug: Check if costCenter field exists in the data
      const recordsWithCostCenter = ccData.filter(record => record.costCenter && record.costCenter.trim() !== '');
      console.log(`Tally: Records with valid cost center: ${recordsWithCostCenter.length}`);

      const allCostCenters = getUniqueCostCenters(ccData, 'costCenter');
      console.log(`Tally: Setting ${allCostCenters.length} unique cost centers for modal`);
      console.log('Tally: All unique cost centers:', allCostCenters);
      console.log('Tally: First 10 cost centers:', allCostCenters.slice(0, 10));
      console.log('Tally: Last 10 cost centers:', allCostCenters.slice(-10));

      if (allCostCenters.length === 0) {
        console.warn('Tally: No unique cost centers found! Check if the costCenter field exists in the data.');
      }

      setUniqueCostCenters(allCostCenters);

      // Clear the selected month if it no longer exists.
      if (month && !unique.includes(month)) {
        setMonth("");
      }
      // If no month is selected and there is at least one, default to the first
      if (!month && unique.length > 0) {
        setMonth(unique[0]);
      }
    } catch (error) {
      console.error('Tally: Error loading cost center data:', error);
      setUniqueCostCenters([]);
      setUniqueMonths([]);
    }
  }, [month]);

  // Fetch unique months from cost center data and update local state
  useEffect(() => {
    fetchUniqueMonths();
  }, []); // Run once on mount

  // Listen for database changes to reload cost center data
  useEffect(() => {
    const handleDBChange = useCallback(async () => {
      console.log('Tally: Database change detected, reloading cost center data...');
      try {
        const ccData = await loadCostCenterData();
        console.log(`Tally: Reloaded ${ccData.length} cost center records after DB change`);

        const allCostCenters = getUniqueCostCenters(ccData, 'costCenter');
        console.log(`Tally: Updated unique cost centers to ${allCostCenters.length} after DB change`);
        setUniqueCostCenters(allCostCenters);
      } catch (error) {
        console.error('Tally: Error reloading cost center data after DB change:', error);
      }
    }, []);

    window.addEventListener("dbChange", handleDBChange);
    return () => window.removeEventListener("dbChange", handleDBChange);
  }, []);

  // Memoized calculation function
  const calculateResourceCost = useCallback(async () => {
    if (!month) return;
    setLoading(true);
    try {
      const ccData = await loadCostCenterData();
      const salaryData = await loadSalaryData();

      // Filter cost center data for the selected month
      const filteredCC = ccData.filter(record => record.month && record.month.trim() === month);

      // Get unique cost centers for this month's calculation
      const costCentersSet = new Set();
      filteredCC.forEach(record => {
        if (record.costCenter) {
          costCentersSet.add(record.costCenter.trim());
        }
      });
      const costCenters = Array.from(costCentersSet);

      // Pre-calculate employee total hours to avoid repeated calculations
      const employeeHours = new Map();
      filteredCC.forEach(record => {
        const empName = record.name?.trim().toLowerCase();
        if (!empName) return;
        const hrs = parseFloat(record.hoursWorked || record.hours || 0);
        employeeHours.set(empName, (employeeHours.get(empName) || 0) + hrs);
      });

      // Pre-index salary data for faster lookup
      const salaryMap = new Map();
      salaryData.forEach(record => {
        if (record.payrollMonth && record.payrollMonth.trim() === month && record.name) {
          const empName = record.name.trim().toLowerCase();
          salaryMap.set(empName, {
            gross: parseFloat(record.gross || 0),
            pf: parseFloat(record.pf || 0)
          });
        }
      });

      // Calculate totals per cost center based on each employee's hours
      const ccTotals = {};
      filteredCC.forEach(record => {
        const cc = record.costCenter ? record.costCenter.trim() : "";
        if (!cc) return;

        const hrs = parseFloat(record.hoursWorked || record.hours || 0);
        if (hrs <= 0) return;

        const empName = record.name?.trim().toLowerCase();
        if (!empName) return;

        const totalHours = employeeHours.get(empName) || 0;
        const salary = salaryMap.get(empName);

        if (!salary || totalHours <= 0) return;

        // Compute cost for this record
        const cost = {
          gross: (salary.gross / totalHours) * hrs,
          pf: (salary.pf / totalHours) * hrs
        };

        if (!ccTotals[cc]) {
          ccTotals[cc] = { gross: 0, pf: 0 };
        }
        ccTotals[cc].gross += cost.gross;
        ccTotals[cc].pf += cost.pf;
      });

      // Calculate overall salary totals for the month
      const salaryTotals = Array.from(salaryMap.values()).reduce(
        (totals, salary) => ({
          gross: totals.gross + salary.gross,
          pf: totals.pf + salary.pf
        }),
        { gross: 0, pf: 0 }
      );

      // Prepare aggregated data array from costCenters and totals
      const aggregated = costCenters.map(cc => ({
        costCenter: applyCostCenterMapping(cc, costCenterMappings),
        originalCostCenter: cc,
        gross: ccTotals[cc] ? ccTotals[cc].gross : 0,
        pf: ccTotals[cc] ? ccTotals[cc].pf : 0,
      }));

      // Calculate differences for each cost center
      const differences = aggregated.map(item => ({
        ...item,
        grossDiff: salaryTotals.gross - item.gross,
        pfDiff: salaryTotals.pf - item.pf,
        salaryGross: salaryTotals.gross,
        salaryPF: salaryTotals.pf
      }));

      setAggregatedData(aggregated);
      setDifferenceData(differences);
      setTotals({
        ccGross: aggregated.reduce((sum, item) => sum + item.gross, 0),
        ccPF: aggregated.reduce((sum, item) => sum + item.pf, 0),
        salaryGross: salaryTotals.gross,
        salaryPF: salaryTotals.pf
      });
    } catch (error) {
      console.error("Error calculating resource cost:", error);
      setAggregatedData([]);
      setDifferenceData([]);
      setTotals({ ccGross: 0, ccPF: 0, salaryGross: 0, salaryPF: 0 });
    } finally {
      setLoading(false);
    }
  }, [month, costCenterMappings]);

  // Calculate tally data based on selected month
  useEffect(() => {
    calculateResourceCost();
  }, [calculateResourceCost]);

  // Memoized export function
  const handleExport = useCallback(() => {
    if (!month || aggregatedData.length === 0) return;

    const wb = XLSX.utils.book_new();

    // First worksheet - Tally Data without employees column
    const tallyData = [
      ["Sl No", "Cost Center", "Original Cost Center", "Gross Salary", "PF"],
      ...aggregatedData.map((row, idx) => [
        idx + 1,
        row.costCenter || "",
        row.originalCostCenter || row.costCenter || "",
        (row.gross || 0).toFixed(2),
        (row.pf || 0).toFixed(2),
      ]),
      [
        "Total",
        "",
        "",
        totals.ccGross.toFixed(2),
        totals.ccPF.toFixed(2),
      ],
    ];
    const ws1 = XLSX.utils.aoa_to_sheet(tallyData);
    XLSX.utils.book_append_sheet(wb, ws1, "Tally Data");

    // Second worksheet - Differences
    const differencesData = [
      [
        "Sl No",
        "Cost Center",
        "Original Cost Center",
        "Cost Center Gross",
        "Salary Gross",
        "Gross Difference",
        "Cost Center PF",
        "Salary PF",
        "PF Difference",
      ],
      ...differenceData.map((row, idx) => [
        idx + 1,
        row.costCenter || "",
        row.originalCostCenter || row.costCenter || "",
        (row.gross || 0).toFixed(2),
        (row.salaryGross || 0).toFixed(2),
        (row.grossDiff || 0).toFixed(2),
        (row.pf || 0).toFixed(2),
        (row.salaryPF || 0).toFixed(2),
        (row.pfDiff || 0).toFixed(2),
      ]),
      [
        "Total",
        "",
        "",
        totals.ccGross.toFixed(2),
        totals.salaryGross.toFixed(2),
        ((totals.ccGross || 0) - (totals.salaryGross || 0)).toFixed(2),
        totals.ccPF.toFixed(2),
        totals.salaryPF.toFixed(2),
        ((totals.ccPF || 0) - (totals.salaryPF || 0)).toFixed(2),
      ],
    ];
    const ws2 = XLSX.utils.aoa_to_sheet(differencesData);
    XLSX.utils.book_append_sheet(wb, ws2, "Differences");

    XLSX.writeFile(wb, `Tally_Report_${month}.xlsx`);
  }, [month, aggregatedData, differenceData, totals]);

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Container sx={{ padding: 4 }}>
        <Card sx={{ mb: 4, overflow: 'visible' }}>
          <CardContent>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant="h5" component="h1" gutterBottom sx={{ mb: 0 }}>
                Tally Output
              </Typography>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Tooltip title="Configure Cost Center Mappings">
                  <IconButton
                    onClick={() => setMappingModalOpen(true)}
                    color="primary"
                    sx={{ mr: 1 }}
                  >
                    <SettingsIcon />
                  </IconButton>
                </Tooltip>
                <Button variant="contained" onClick={() => { setMonth(""); }}>
                  Refresh Data
                </Button>
              </Box>
            </Box>

            <Box sx={{ mb: 3, width: "100%" }}>
              <FormControl fullWidth>
                <InputLabel>Month</InputLabel>
                <Select
                  value={month}
                  onChange={(e) => setMonth(e.target.value)}
                  label="Month"
                >
                  {uniqueMonths.map((m) => (
                    <MenuItem key={m} value={m}>
                      {m}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Box>
          </CardContent>
        </Card>

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            <Card sx={{ mb: 4 }}>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  Aggregated Data (Cost Center Totals)
                </Typography>
                {aggregatedData.length === 0 ? (
                  <Typography>No aggregated data available for this month.</Typography>
                ) : (
                  <TableContainer component={Paper} sx={{ boxShadow: 'none' }}>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>Sl No</TableCell>
                          <TableCell>Cost Center</TableCell>
                          <TableCell>Gross Salary</TableCell>
                          <TableCell>PF</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {aggregatedData.map((row, index) => (
                          <TableRow key={index} sx={{ "&:nth-of-type(even)": { backgroundColor: "#f4f6f8" } }}>
                            <TableCell>{index + 1}</TableCell>
                            <TableCell>{row.costCenter || ""}</TableCell>
                            <TableCell>{(row.gross || 0).toFixed(2)}</TableCell>
                            <TableCell>{(row.pf || 0).toFixed(2)}</TableCell>
                          </TableRow>
                        ))}
                        <TableRow>
                          <TableCell colSpan={2} sx={{ fontWeight: "bold" }}>
                            Total
                          </TableCell>
                          <TableCell sx={{ fontWeight: "bold" }}>
                            {(totals.ccGross || 0).toFixed(2)}
                          </TableCell>
                          <TableCell sx={{ fontWeight: "bold" }}>
                            {(totals.ccPF || 0).toFixed(2)}
                          </TableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                  </TableContainer>
                )}
              </CardContent>
            </Card>

            <Card sx={{ mb: 4 }}>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  Salary vs Cost Center Comparison
                </Typography>
                <TableContainer component={Paper} sx={{ boxShadow: 'none' }}>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Sl No</TableCell>
                        <TableCell>Cost Center</TableCell>
                        <TableCell>Cost Center Gross</TableCell>
                        <TableCell>Salary Gross</TableCell>
                        <TableCell>Gross Difference</TableCell>
                        <TableCell>Cost Center PF</TableCell>
                        <TableCell>Salary PF</TableCell>
                        <TableCell>PF Difference</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {differenceData.map((row, index) => (
                        <TableRow key={index} sx={{ "&:nth-of-type(even)": { backgroundColor: "#f4f6f8" } }}>
                          <TableCell>{index + 1}</TableCell>
                          <TableCell>{row.costCenter || ""}</TableCell>
                          <TableCell>{(row.gross || 0).toFixed(2)}</TableCell>
                          <TableCell>{(row.salaryGross || 0).toFixed(2)}</TableCell>
                          <TableCell sx={{ color: row.grossDiff !== 0 ? "red" : "inherit" }}>
                            {(row.grossDiff || 0).toFixed(2)}
                          </TableCell>
                          <TableCell>{(row.pf || 0).toFixed(2)}</TableCell>
                          <TableCell>{(row.salaryPF || 0).toFixed(2)}</TableCell>
                          <TableCell sx={{ color: row.pfDiff !== 0 ? "red" : "inherit" }}>
                            {(row.pfDiff || 0).toFixed(2)}
                          </TableCell>
                        </TableRow>
                      ))}
                      <TableRow>
                        <TableCell colSpan={2} sx={{ fontWeight: "bold" }}>
                          Total
                        </TableCell>
                        <TableCell sx={{ fontWeight: "bold" }}>
                          {(totals.ccGross || 0).toFixed(2)}
                        </TableCell>
                        <TableCell sx={{ fontWeight: "bold" }}>
                          {(totals.salaryGross || 0).toFixed(2)}
                        </TableCell>
                        <TableCell
                          sx={{
                            fontWeight: "bold",
                            color: (totals.ccGross - totals.salaryGross) !== 0 ? "red" : "inherit",
                          }}
                        >
                          {((totals.ccGross || 0) - (totals.salaryGross || 0)).toFixed(2)}
                        </TableCell>
                        <TableCell sx={{ fontWeight: "bold" }}>
                          {(totals.ccPF || 0).toFixed(2)}
                        </TableCell>
                        <TableCell sx={{ fontWeight: "bold" }}>
                          {(totals.salaryPF || 0).toFixed(2)}
                        </TableCell>
                        <TableCell
                          sx={{
                            fontWeight: "bold",
                            color: (totals.ccPF - totals.salaryPF) !== 0 ? "red" : "inherit",
                          }}
                        >
                          {((totals.ccPF || 0) - (totals.salaryPF || 0)).toFixed(2)}
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              </CardContent>
            </Card>

            <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
              <Button
                variant="contained"
                onClick={handleExport}
                sx={{ px: 3 }}
              >
                Export as Excel
              </Button>
            </Box>
          </>
        )}

        {/* Cost Center Mapping Modal */}
        <CostCenterMappingModal
          open={mappingModalOpen}
          onClose={useCallback(() => setMappingModalOpen(false), [])}
          costCenters={uniqueCostCenters}
        />
      </Container>
    </motion.div>
  );
};

export default memo(Tally);
